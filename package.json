{"name": "applied4", "version": "0.0.0", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"rxjs": "^7.8.2"}, "devDependencies": {"@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "chai": "^4.3.3", "mocha": "^10.2.0", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite": "^7.0.1", "vite-plugin-checker": "^0.9.3", "vite-plugin-node-polyfills": "^0.24.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}}