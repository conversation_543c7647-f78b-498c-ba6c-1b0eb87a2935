<!doctype html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>Week 4 Exercises</title>
        <!--load this first to set the theme to avoid white-dark flicker-->
        <script type="module" src="./tests/preload.ts"></script>

        <style>
            #gameCanvas {
                position: relative;
                width: 400px;
                height: 400px;
                border: 1px solid #ccc;
                margin: 1rem 0 1rem 5%;
                background-color: #f8f8f8;
                overflow: hidden;
            }

            [data-theme="dark"] #gameCanvas {
                background-color: #222;
                border-color: #555;
            }

            #dot {
                position: absolute;
                left: 190px; /* centered in 400px wide canvas with 20px dot */
                width: 20px;
                height: 20px;
                background: red;
                border-radius: 50%;
            }
        </style>

        <link href="node_modules/mocha/mocha.css" rel="stylesheet" />
        <link rel="stylesheet" href="./src/style.css" />
    </head>

    <body>
        <div class="sliderWrapper">
            <div>
                <p>Dark Mode&nbsp;&nbsp;</p>
            </div>
            <label class="switch">
                <input type="checkbox" id="light_vs_dark_toggle" />
                <span class="slider"></span>
            </label>
        </div>

        <main>
            <div class="description">
                <h1>Week 4 - Pure RNG and state</h1>

                <h2 class="heading">
                    Exercise 1 - Creating a random number stream
                </h2>
                <div class="tests" id="exercise_1_suite"></div>

                <h2 class="heading">
                    Exercises 2-6 - Creating an interactive jumping dot.
                </h2>
                <div id="gameCanvas">
                    <div id="dot"></div>
                </div>
                <label for="numBounces">Number of bounces:</label
                ><output id="numBounces">0</output>
            </div>
        </main>

        <div id="mocha" class="test"></div>
        <script type="module" src="./src/main.ts"></script>
        <script type="module" src="./tests/main.test.ts"></script>
        <script type="module" src="./tests/index.ts"></script>
    </body>
</html>
